#property strict

#include "../TestFramework.mqh"
#include "MockTradingPipeline.mqh"
#include "../../TradingPipelineDriver.mqh"
#include "../../MainPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體主流水線實現                                         |
//+------------------------------------------------------------------+
class TestMainPipelineImpl : public MainPipeline
{
private:
    bool m_shouldSucceed;
    int m_executionCount;

public:
    TestMainPipelineImpl(string name = "TestMainPipeline",
                        bool shouldSucceed = true,
                        ENUM_TRADING_STAGE stage = INIT_START,
                        ITradingPipelineDriver* driver = NULL)
        : MainPipeline(name, "TestMainPipeline", stage, driver),
          m_shouldSucceed(shouldSucceed),
          m_executionCount(0) {}

    int GetExecutionCount() const { return m_executionCount; }
    void SetShouldSucceed(bool shouldSucceed) { m_shouldSucceed = shouldSucceed; }

protected:
    virtual void Main() override
    {
        m_executionCount++;
        if(m_shouldSucceed)
        {
            Print("✅ TestMainPipeline 執行成功: ", GetName());
        }
        else
        {
            Print("❌ TestMainPipeline 執行失敗: ", GetName());
        }
    }
};

//+------------------------------------------------------------------+
//| 端到端工作流程測試類                                             |
//+------------------------------------------------------------------+
class TestTradingPipelineWorkflow : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineWorkflow(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineWorkflow"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestCompleteWorkflow();
        TestMainPipelineWorkflow();
        TestMultiStageWorkflow();
        TestEventDrivenWorkflow();
        TestErrorRecoveryWorkflow();
        TestPerformanceWorkflow();
        TestConcurrentWorkflow();
    }

private:
    // 測試完整工作流程
    void TestCompleteWorkflow()
    {
        SetUp();

        // 1. 獲取驅動器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineRegistry* registry = driver.GetRegistry();
        TradingPipelineExplorer* explorer = driver.GetExplorer();
        TradingPipelineContainerManager* manager = driver.GetManager();

        // 2. 創建完整的交易流水線工作流程
        Vector<MockTradingPipeline*> pipelines;
        Vector<ENUM_TRADING_STAGE> stages;
        TestDataFactory::CreateTestStages(stages);

        // 創建每個階段的流水線
        for(int i = 0; i < stages.size(); i++)
        {
            string name = "Stage_" + EnumToString(stages.at(i));
            MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline(name, stages.at(i), driver);
            pipelines.push_back(pipeline);
            
            // 註冊流水線
            registry.Register(pipeline);
        }

        // 3. 按順序執行所有階段
        for(int i = 0; i < stages.size(); i++)
        {
            ITradingPipeline* pipeline = explorer.GetPipeline(stages.at(i));
            if(pipeline != NULL)
            {
                pipeline.Execute();
            }
        }

        // 4. 驗證完整工作流程
        if(m_runner != NULL)
        {
            bool allExecuted = true;
            for(int i = 0; i < pipelines.size(); i++)
            {
                if(!pipelines.at(i).IsExecuted())
                {
                    allExecuted = false;
                    break;
                }
            }

            m_runner.RecordResult(Assert::AssertTrue("完整工作流程_所有階段執行", allExecuted));
            m_runner.RecordResult(Assert::AssertEquals("完整工作流程_階段數量", stages.size(), pipelines.size()));
        }

        // 清理
        MockTradingPipelineFactory::CleanupPipelines(pipelines);

        TearDown();
    }

    // 測試主流水線工作流程
    void TestMainPipelineWorkflow()
    {
        SetUp();

        // 創建主流水線
        TestMainPipelineImpl* mainPipeline = new TestMainPipelineImpl("MainWorkflowTest", true, INIT_START);

        // 執行主流水線
        mainPipeline.Execute();

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("主流水線工作流程_執行完成", mainPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("主流水線工作流程_執行次數", 1, mainPipeline.GetExecutionCount()));
            m_runner.RecordResult(Assert::AssertNotNull("主流水線工作流程_驅動器存在", mainPipeline.GetDriver()));
        }

        // 清理
        delete mainPipeline;

        TearDown();
    }

    // 測試多階段工作流程
    void TestMultiStageWorkflow()
    {
        SetUp();

        // 獲取驅動器組件
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineContainerManager* manager = driver.GetManager();

        // 創建多階段容器
        Vector<TradingPipelineContainer*> containers;
        Vector<ENUM_TRADING_EVENT> events;
        TestDataFactory::CreateTestEvents(events);

        for(int i = 0; i < MathMin(3, events.size()); i++)  // 限制為3個階段
        {
            string containerName = "MultiStage_Container_" + IntegerToString(i + 1);
            TradingPipelineContainer* container = new TradingPipelineContainer(
                containerName,
                "多階段測試容器",
                "TradingPipelineContainer",
                events.at(i)
            );

            // 為每個容器添加流水線
            for(int j = 0; j < 2; j++)  // 每個容器2個流水線
            {
                string pipelineName = containerName + "_Pipeline_" + IntegerToString(j + 1);
                MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline(pipelineName);
                container.AddPipeline(pipeline);
            }

            containers.push_back(container);
            manager.SetContainer(events.at(i), container);
        }

        // 按順序執行各階段
        for(int i = 0; i < MathMin(3, events.size()); i++)
        {
            manager.Execute(events.at(i));
        }

        // 驗證多階段執行
        if(m_runner != NULL)
        {
            bool allContainersExecuted = true;
            for(int i = 0; i < containers.size(); i++)
            {
                if(!containers.at(i).IsExecuted())
                {
                    allContainersExecuted = false;
                    break;
                }
            }

            m_runner.RecordResult(Assert::AssertTrue("多階段工作流程_所有容器執行", allContainersExecuted));
            m_runner.RecordResult(Assert::AssertEquals("多階段工作流程_容器數量", 3, containers.size()));
        }

        // 清理
        for(int i = 0; i < containers.size(); i++)
        {
            delete containers.at(i);
        }

        TearDown();
    }

    // 測試事件驅動工作流程
    void TestEventDrivenWorkflow()
    {
        SetUp();

        // 獲取驅動器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineContainerManager* manager = driver.GetManager();

        // 創建事件驅動的容器
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer(
            "TickEventContainer", "Tick事件容器", "TradingPipelineContainer", TRADING_TICK
        );
        TradingPipelineContainer* signalContainer = new TradingPipelineContainer(
            "SignalEventContainer", "信號事件容器", "TradingPipelineContainer", TRADING_SIGNAL
        );

        // 添加流水線
        MockTradingPipeline* tickPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("TickProcessor");
        MockTradingPipeline* signalPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("SignalProcessor");

        tickContainer.AddPipeline(tickPipeline);
        signalContainer.AddPipeline(signalPipeline);

        // 設置到管理器
        manager.SetContainer(TRADING_TICK, tickContainer);
        manager.SetContainer(TRADING_SIGNAL, signalContainer);

        // 模擬事件驅動執行
        manager.Execute(TRADING_TICK);    // 處理Tick事件
        manager.Execute(TRADING_SIGNAL);  // 處理信號事件

        // 驗證事件驅動執行
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("事件驅動工作流程_Tick容器執行", tickContainer.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("事件驅動工作流程_信號容器執行", signalContainer.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("事件驅動工作流程_Tick流水線執行", tickPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("事件驅動工作流程_信號流水線執行", signalPipeline.IsExecuted()));
        }

        // 清理
        delete tickContainer;
        delete signalContainer;
        delete tickPipeline;
        delete signalPipeline;

        TearDown();
    }

    // 測試錯誤恢復工作流程
    void TestErrorRecoveryWorkflow()
    {
        SetUp();

        // 創建包含錯誤的工作流程
        TradingPipelineContainer* container = new TradingPipelineContainer(
            "ErrorRecoveryContainer", "錯誤恢復測試容器", "TradingPipelineContainer", TRADING_RISK
        );

        // 創建成功和失敗的流水線
        MockTradingPipeline* successPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("SuccessPipeline");
        MockTradingPipeline* failedPipeline = MockTradingPipelineFactory::CreateFailedPipeline("FailedPipeline", "模擬錯誤");
        MockTradingPipeline* recoveryPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("RecoveryPipeline");

        container.AddPipeline(successPipeline);
        container.AddPipeline(failedPipeline);
        container.AddPipeline(recoveryPipeline);

        // 執行容器
        container.Execute();

        // 驗證錯誤處理
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("錯誤恢復工作流程_容器執行完成", container.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("錯誤恢復工作流程_成功流水線執行", successPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("錯誤恢復工作流程_失敗流水線執行", failedPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("錯誤恢復工作流程_恢復流水線執行", recoveryPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertFalse("錯誤恢復工作流程_失敗流水線結果", failedPipeline.GetResult().IsSuccess()));
        }

        // 清理
        delete container;
        delete successPipeline;
        delete failedPipeline;
        delete recoveryPipeline;

        TearDown();
    }

    // 測試性能工作流程
    void TestPerformanceWorkflow()
    {
        SetUp();

        const int PIPELINE_COUNT = 50;  // 大量流水線測試性能

        // 創建大型容器
        TradingPipelineContainer* performanceContainer = new TradingPipelineContainer(
            "PerformanceContainer", "性能測試容器", "TradingPipelineContainer", TRADING_POSITION
        );

        Vector<MockTradingPipeline*> pipelines;
        MockTradingPipelineFactory::CreateTestPipelines(pipelines, PIPELINE_COUNT, "PerfPipeline");

        // 添加所有流水線到容器
        for(int i = 0; i < pipelines.size(); i++)
        {
            performanceContainer.AddPipeline(pipelines.at(i));
        }

        // 記錄開始時間
        uint startTime = GetTickCount();

        // 執行容器
        performanceContainer.Execute();

        // 記錄結束時間
        uint endTime = GetTickCount();
        uint executionTime = endTime - startTime;

        // 驗證性能
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("性能工作流程_容器執行完成", performanceContainer.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("性能工作流程_流水線數量", PIPELINE_COUNT, performanceContainer.GetPipelineCount()));
            
            // 驗證所有流水線都執行了
            bool allExecuted = true;
            for(int i = 0; i < pipelines.size(); i++)
            {
                if(!pipelines.at(i).IsExecuted())
                {
                    allExecuted = false;
                    break;
                }
            }
            m_runner.RecordResult(Assert::AssertTrue("性能工作流程_所有流水線執行", allExecuted));
            
            Print("性能測試: ", PIPELINE_COUNT, " 個流水線執行時間: ", executionTime, " 毫秒");
        }

        // 清理
        delete performanceContainer;
        MockTradingPipelineFactory::CleanupPipelines(pipelines);

        TearDown();
    }

    // 測試併發工作流程
    void TestConcurrentWorkflow()
    {
        SetUp();

        // 獲取驅動器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineContainerManager* manager = driver.GetManager();

        // 創建多個獨立的容器模擬併發
        TradingPipelineContainer* container1 = new TradingPipelineContainer(
            "ConcurrentContainer1", "併發容器1", "TradingPipelineContainer", TRADING_ORDER
        );
        TradingPipelineContainer* container2 = new TradingPipelineContainer(
            "ConcurrentContainer2", "併發容器2", "TradingPipelineContainer", TRADING_POSITION
        );

        // 添加流水線
        MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateDelayedPipeline("DelayedPipeline1", 10);
        MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateDelayedPipeline("DelayedPipeline2", 15);

        container1.AddPipeline(pipeline1);
        container2.AddPipeline(pipeline2);

        // 設置到管理器
        manager.SetContainer(TRADING_ORDER, container1);
        manager.SetContainer(TRADING_POSITION, container2);

        // 記錄開始時間
        uint startTime = GetTickCount();

        // 執行兩個容器（模擬併發）
        manager.Execute(TRADING_ORDER);
        manager.Execute(TRADING_POSITION);

        // 記錄結束時間
        uint endTime = GetTickCount();
        uint totalTime = endTime - startTime;

        // 驗證併發執行
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("併發工作流程_容器1執行", container1.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("併發工作流程_容器2執行", container2.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("併發工作流程_流水線1執行", pipeline1.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("併發工作流程_流水線2執行", pipeline2.IsExecuted()));
            
            Print("併發測試總執行時間: ", totalTime, " 毫秒");
        }

        // 清理
        delete container1;
        delete container2;
        delete pipeline1;
        delete pipeline2;

        TearDown();
    }
};
