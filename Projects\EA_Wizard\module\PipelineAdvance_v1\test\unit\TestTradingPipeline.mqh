//+------------------------------------------------------------------+
//|                                           TestTradingPipeline.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                    |
//+------------------------------------------------------------------+
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipeline.mqh"
#include "../../TradingPipelineDriver.mqh"
#include "../integration/MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| TradingPipeline 單元測試類                                       |
//+------------------------------------------------------------------+
class TestTradingPipeline : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipeline(TestRunner* runner = NULL)
        : TestCase("TestTradingPipeline"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipeline() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipeline 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestExecuteFlow();
        TestRestoreFunction();
        TestStageAndRegistry();

        Print("=== TradingPipeline 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipeline 構造函數 ---");

        // 測試默認構造函數
        MockTradingPipeline* pipeline1 = new MockTradingPipeline();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestConstructor - 默認構造函數",
                pipeline1 != NULL,
                pipeline1 != NULL ? "構造函數成功" : "構造函數失敗"
            ));
        }

        // 測試帶參數的構造函數
        MockTradingPipeline* pipeline2 = new MockTradingPipeline("TestPipeline", "TestType", INIT_COMPLETE);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestConstructor - 帶參數構造函數",
                pipeline2 != NULL && pipeline2.GetName() == "TestPipeline",
                pipeline2 != NULL ? "構造函數成功，名稱正確" : "構造函數失敗"
            ));
        }

        delete pipeline1;
        delete pipeline2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipeline 基本屬性 ---");

        MockTradingPipeline* pipeline = new MockTradingPipeline("TestName", true, INIT_COMPLETE);

        // 測試 GetName
        string name = pipeline.GetName();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestBasicProperties - GetName",
                name == "TestName",
                name == "TestName" ? "名稱正確" : "名稱錯誤: " + name
            ));
        }

        // 測試 GetType
        string type = pipeline.GetType();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestBasicProperties - GetType",
                type == "MockTradingPipeline",
                type == "MockTradingPipeline" ? "類型正確" : "類型錯誤: " + type
            ));
        }

        // 測試初始執行狀態
        bool executed = pipeline.IsExecuted();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestBasicProperties - 初始執行狀態",
                executed == false,
                !executed ? "初始狀態正確（未執行）" : "初始狀態錯誤（已執行）"
            ));
        }

        // 測試 GetStage
        ENUM_TRADING_STAGE stage = pipeline.GetStage();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestBasicProperties - GetStage",
                stage == INIT_COMPLETE,
                stage == INIT_COMPLETE ? "階段正確" : "階段錯誤"
            ));
        }

        delete pipeline;
    }

    // 測試執行流程
    void TestExecuteFlow()
    {
        Print("--- 測試 TradingPipeline 執行流程 ---");

        MockTradingPipeline* pipeline = new MockTradingPipeline("ExecuteTest", true, INIT_START);

        // 測試首次執行
        pipeline.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestExecuteFlow - 首次執行",
                pipeline.IsExecuted() == true,
                pipeline.IsExecuted() ? "執行狀態正確" : "執行狀態錯誤"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestExecuteFlow - Main 方法調用次數",
                pipeline.GetExecutionCount() == 1,
                pipeline.GetExecutionCount() == 1 ? "Main 方法調用一次" : "Main 方法調用次數錯誤: " + IntegerToString(pipeline.GetExecutionCount())
            ));
        }

        // 測試重複執行（應該被跳過）
        pipeline.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestExecuteFlow - 重複執行防護",
                pipeline.GetExecutionCount() == 1,
                pipeline.GetExecutionCount() == 1 ? "重複執行被正確跳過" : "重複執行未被跳過"
            ));
        }

        delete pipeline;
    }

    // 測試重置功能
    void TestRestoreFunction()
    {
        Print("--- 測試 TradingPipeline 重置功能 ---");

        MockTradingPipeline* pipeline = new MockTradingPipeline("RestoreTest", true);

        // 先執行
        pipeline.Execute();
        bool executedBefore = pipeline.IsExecuted();

        // 然後重置
        pipeline.Restore();
        bool executedAfter = pipeline.IsExecuted();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestRestoreFunction - 重置功能",
                executedBefore == true && executedAfter == false,
                (executedBefore && !executedAfter) ? "重置功能正常" : "重置功能異常"
            ));
        }

        // 測試重置後可以再次執行
        pipeline.Execute();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestRestoreFunction - 重置後再執行",
                pipeline.IsExecuted() == true && pipeline.GetExecutionCount() == 2,
                "重置後可以再次執行"
            ));
        }

        delete pipeline;
    }

    // 測試階段和驅動器
    void TestStageAndRegistry()
    {
        Print("--- 測試 TradingPipeline 階段和驅動器 ---");

        MockTradingPipeline* pipeline = new MockTradingPipeline("StageTest", true, TICK_DATA_FEED);

        // 測試階段設置
        ENUM_TRADING_STAGE stage = pipeline.GetStage();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestStageAndRegistry - 階段設置",
                stage == TICK_DATA_FEED,
                stage == TICK_DATA_FEED ? "階段設置正確" : "階段設置錯誤"
            ));
        }

        // 測試驅動器（應該不為 NULL，因為使用預設的 GetInstance()）
        ITradingPipelineDriver* driver = pipeline.GetDriver();
        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipeline::TestStageAndRegistry - 驅動器不為空",
                driver != NULL,
                driver != NULL ? "驅動器正確獲取" : "驅動器為空"
            ));
        }

        delete pipeline;
    }
};
