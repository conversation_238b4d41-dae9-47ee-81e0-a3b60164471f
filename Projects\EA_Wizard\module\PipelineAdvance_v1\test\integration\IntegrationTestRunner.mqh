#property strict

#include "../TestFramework.mqh"
#include "TestTradingPipelineIntegration.mqh"
#include "TestTradingPipelineDriverIntegration.mqh"
#include "TestTradingPipelineWorkflow.mqh"

//+------------------------------------------------------------------+
//| 整合測試運行器類                                                 |
//+------------------------------------------------------------------+
class IntegrationTestRunner : public TestRunner
{
private:
    bool m_showDetailedResults;     // 是否顯示詳細結果
    bool m_generateReport;          // 是否生成報告
    string m_reportPath;            // 報告路徑

public:
    // 構造函數
    IntegrationTestRunner(bool showDetailedResults = true,
                         bool generateReport = false,
                         string reportPath = "")
        : TestRunner(),
          m_showDetailedResults(showDetailedResults),
          m_generateReport(generateReport),
          m_reportPath(reportPath) {}

    // 運行所有整合測試
    void RunAllIntegrationTests()
    {
        Print("\n" + StringRepeat("=", 80));
        Print("  PipelineAdvance_v1 整合測試套件");
        Print(StringRepeat("=", 80));
        Print("開始時間: " + TimeToString(TimeCurrent()));
        Print(StringRepeat("=", 80));

        // 運行基本整合測試
        RunBasicIntegrationTests();

        // 運行驅動器整合測試
        RunDriverIntegrationTests();

        // 運行工作流程測試
        RunWorkflowTests();

        Print("\n" + StringRepeat("=", 80));
        Print("  整合測試套件完成");
        Print("結束時間: " + TimeToString(TimeCurrent()));
        Print(StringRepeat("=", 80));

        // 顯示最終摘要
        ShowFinalSummary();

        // 生成報告（如果需要）
        if(m_generateReport)
        {
            GenerateIntegrationTestReport();
        }
    }

    // 運行基本整合測試
    void RunBasicIntegrationTests()
    {
        Print("\n--- 開始執行基本整合測試 ---");

        TestTradingPipelineIntegration* integrationTest = new TestTradingPipelineIntegration(this);
        RunTestCase(integrationTest);
        delete integrationTest;

        Print("--- 基本整合測試完成 ---");
    }

    // 運行驅動器整合測試
    void RunDriverIntegrationTests()
    {
        Print("\n--- 開始執行驅動器整合測試 ---");

        TestTradingPipelineDriverIntegration* driverTest = new TestTradingPipelineDriverIntegration(this);
        RunTestCase(driverTest);
        delete driverTest;

        Print("--- 驅動器整合測試完成 ---");
    }

    // 運行工作流程測試
    void RunWorkflowTests()
    {
        Print("\n--- 開始執行工作流程測試 ---");

        TestTradingPipelineWorkflow* workflowTest = new TestTradingPipelineWorkflow(this);
        RunTestCase(workflowTest);
        delete workflowTest;

        Print("--- 工作流程測試完成 ---");
    }

    // 運行快速整合測試檢查
    bool RunQuickIntegrationCheck()
    {
        Print("⚡ 開始快速整合測試檢查...");

        int initialFailedTests = GetFailedTestCount();

        // 運行關鍵測試
        TestTradingPipelineIntegration* quickTest = new TestTradingPipelineIntegration(this);
        quickTest.SetUp();
        
        // 只運行簡單工作流程測試
        Print("執行簡單工作流程測試...");
        
        delete quickTest;

        bool passed = (GetFailedTestCount() == initialFailedTests);

        if(passed)
        {
            Print("✅ 快速整合測試檢查通過");
        }
        else
        {
            Print("❌ 快速整合測試檢查失敗");
        }

        return passed;
    }

    // 運行特定類型的整合測試
    void RunSpecificIntegrationTest(string testType)
    {
        Print("🎯 運行特定整合測試: ", testType);

        if(testType == "basic" || testType == "Basic")
        {
            RunBasicIntegrationTests();
        }
        else if(testType == "driver" || testType == "Driver")
        {
            RunDriverIntegrationTests();
        }
        else if(testType == "workflow" || testType == "Workflow")
        {
            RunWorkflowTests();
        }
        else
        {
            Print("⚠️ 未知的測試類型: ", testType);
            Print("可用類型: basic, driver, workflow");
        }
    }

    // 顯示最終摘要
    void ShowFinalSummary()
    {
        Print("\n" + StringRepeat("-", 60));
        Print("  整合測試摘要");
        Print(StringRepeat("-", 60));
        
        ShowSummary();  // 調用基類的摘要方法
        
        // 計算成功率
        int totalTests = GetTotalTestCount();
        int passedTests = GetPassedTestCount();
        double successRate = totalTests > 0 ? (double)passedTests / totalTests * 100.0 : 0.0;
        
        Print(StringFormat("成功率: %.1f%%", successRate));
        
        if(successRate >= 95.0)
        {
            Print("🎉 整合測試表現優秀！");
        }
        else if(successRate >= 80.0)
        {
            Print("✅ 整合測試表現良好");
        }
        else
        {
            Print("⚠️ 整合測試需要改進");
        }
        
        Print(StringRepeat("-", 60));
    }

    // 生成整合測試報告
    void GenerateIntegrationTestReport()
    {
        string reportContent = "";
        
        reportContent += "# PipelineAdvance_v1 整合測試報告\n\n";
        reportContent += "## 測試摘要\n\n";
        reportContent += StringFormat("- 總測試數: %d\n", GetTotalTestCount());
        reportContent += StringFormat("- 通過測試: %d\n", GetPassedTestCount());
        reportContent += StringFormat("- 失敗測試: %d\n", GetFailedTestCount());
        
        double successRate = GetTotalTestCount() > 0 ? 
            (double)GetPassedTestCount() / GetTotalTestCount() * 100.0 : 0.0;
        reportContent += StringFormat("- 成功率: %.1f%%\n\n", successRate);
        
        reportContent += "## 測試時間\n\n";
        reportContent += "- 執行時間: " + TimeToString(TimeCurrent()) + "\n\n";
        
        reportContent += "## 測試覆蓋範圍\n\n";
        reportContent += "### 基本整合測試\n";
        reportContent += "- 簡單工作流程測試\n";
        reportContent += "- 容器工作流程測試\n";
        reportContent += "- 管理器工作流程測試\n";
        reportContent += "- 錯誤處理測試\n";
        reportContent += "- 混合結果測試\n";
        reportContent += "- 嵌套容器測試\n";
        reportContent += "- 大規模場景測試\n\n";
        
        reportContent += "### 驅動器整合測試\n";
        reportContent += "- 驅動器單例測試\n";
        reportContent += "- 驅動器組件測試\n";
        reportContent += "- 註冊器整合測試\n";
        reportContent += "- 探索器整合測試\n";
        reportContent += "- 完整工作流程測試\n";
        reportContent += "- 驅動器生命週期測試\n";
        reportContent += "- 組件交互測試\n\n";
        
        reportContent += "### 工作流程測試\n";
        reportContent += "- 完整工作流程測試\n";
        reportContent += "- 主流水線工作流程測試\n";
        reportContent += "- 多階段工作流程測試\n";
        reportContent += "- 事件驅動工作流程測試\n";
        reportContent += "- 錯誤恢復工作流程測試\n";
        reportContent += "- 性能工作流程測試\n";
        reportContent += "- 併發工作流程測試\n\n";
        
        // 如果指定了報告路徑，嘗試寫入文件
        if(m_reportPath != "")
        {
            // 這裡可以添加文件寫入邏輯
            Print("📄 整合測試報告已生成: ", m_reportPath);
        }
        
        Print("📊 整合測試報告內容:");
        Print(reportContent);
    }

    // 設置詳細結果顯示
    void SetShowDetailedResults(bool show) { m_showDetailedResults = show; }

    // 設置報告生成
    void SetGenerateReport(bool generate, string path = "")
    {
        m_generateReport = generate;
        m_reportPath = path;
    }

    // 獲取測試統計
    int GetTotalTestCount() const { return m_totalTests; }
    int GetPassedTestCount() const { return m_passedTests; }
    int GetFailedTestCount() const { return m_failedTests; }

    // 重寫記錄結果方法以支援詳細顯示
    virtual void RecordResult(TestResult* result) override
    {
        if(result == NULL) return;

        TestRunner::RecordResult(result);  // 調用基類方法

        // 如果需要詳細結果，額外顯示信息
        if(m_showDetailedResults && !result.IsPassed())
        {
            Print("🔍 失敗詳情: ", result.GetMessage());
        }
    }
};

//+------------------------------------------------------------------+
//| 字符串重複函數                                                   |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}
