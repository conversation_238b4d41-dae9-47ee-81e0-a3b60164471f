#property strict

#include "../TestFramework.mqh"
#include "MockTradingPipeline.mqh"
#include "../../TradingPipelineDriver.mqh"
#include "../../TradingPipelineRegistry.mqh"
#include "../../TradingPipelineExplorer.mqh"

//+------------------------------------------------------------------+
//| 驅動器整合測試類                                                 |
//+------------------------------------------------------------------+
class TestTradingPipelineDriverIntegration : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineDriverIntegration(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineDriverIntegration"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestDriverSingleton();
        TestDriverComponents();
        TestRegistryIntegration();
        TestExplorerIntegration();
        TestFullWorkflow();
        TestDriverLifecycle();
        TestComponentInteraction();
    }

private:
    // 測試驅動器單例模式
    void TestDriverSingleton()
    {
        SetUp();

        // 獲取驅動器實例
        TradingPipelineDriver* driver1 = TradingPipelineDriver::GetInstance();
        TradingPipelineDriver* driver2 = TradingPipelineDriver::GetInstance();

        // 驗證單例
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNotNull("驅動器單例_實例1不為空", driver1));
            m_runner.RecordResult(Assert::AssertNotNull("驅動器單例_實例2不為空", driver2));
            m_runner.RecordResult(Assert::AssertEquals("驅動器單例_相同實例", GetPointer(driver1), GetPointer(driver2)));
        }

        TearDown();
    }

    // 測試驅動器組件
    void TestDriverComponents()
    {
        SetUp();

        // 獲取驅動器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

        // 獲取組件
        TradingPipelineContainerManager* manager = driver.GetManager();
        TradingPipelineRegistry* registry = driver.GetRegistry();
        TradingPipelineExplorer* explorer = driver.GetExplorer();

        // 驗證組件
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNotNull("驅動器組件_管理器不為空", manager));
            m_runner.RecordResult(Assert::AssertNotNull("驅動器組件_註冊器不為空", registry));
            m_runner.RecordResult(Assert::AssertNotNull("驅動器組件_探索器不為空", explorer));
            
            // 驗證組件名稱
            if(manager != NULL)
            {
                m_runner.RecordResult(Assert::AssertEquals("驅動器組件_管理器名稱", "TradingPipelineContainerManager", manager.GetName()));
            }
            if(registry != NULL)
            {
                m_runner.RecordResult(Assert::AssertEquals("驅動器組件_註冊器名稱", "TradingPipelineRegistry", registry.GetName()));
            }
            if(explorer != NULL)
            {
                m_runner.RecordResult(Assert::AssertEquals("驅動器組件_探索器名稱", "TradingPipelineExplorer", explorer.GetName()));
            }
        }

        TearDown();
    }

    // 測試註冊器整合
    void TestRegistryIntegration()
    {
        SetUp();

        // 獲取驅動器和註冊器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineRegistry* registry = driver.GetRegistry();

        if(registry != NULL)
        {
            // 創建測試流水線
            MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("TestPipeline1", INIT_START, driver);
            MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("TestPipeline2", TICK_DATA_FEED, driver);

            // 註冊流水線
            bool registered1 = registry.Register(pipeline1);
            bool registered2 = registry.Register(pipeline2);

            // 驗證註冊結果
            if(m_runner != NULL)
            {
                m_runner.RecordResult(Assert::AssertTrue("註冊器整合_流水線1註冊成功", registered1));
                m_runner.RecordResult(Assert::AssertTrue("註冊器整合_流水線2註冊成功", registered2));
            }

            // 清理
            delete pipeline1;
            delete pipeline2;
        }

        TearDown();
    }

    // 測試探索器整合
    void TestExplorerIntegration()
    {
        SetUp();

        // 獲取驅動器組件
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineRegistry* registry = driver.GetRegistry();
        TradingPipelineExplorer* explorer = driver.GetExplorer();

        if(registry != NULL && explorer != NULL)
        {
            // 創建和註冊測試流水線
            MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("ExplorerTestPipeline", SIGNAL_PROCESSING, driver);
            bool registered = registry.Register(pipeline);

            if(registered)
            {
                // 使用探索器查找流水線
                ITradingPipeline* foundPipeline = explorer.GetPipeline(SIGNAL_PROCESSING);

                // 驗證探索結果
                if(m_runner != NULL)
                {
                    m_runner.RecordResult(Assert::AssertNotNull("探索器整合_找到流水線", foundPipeline));
                    if(foundPipeline != NULL)
                    {
                        m_runner.RecordResult(Assert::AssertEquals("探索器整合_流水線名稱", "ExplorerTestPipeline", foundPipeline.GetName()));
                    }
                }
            }

            // 清理
            delete pipeline;
        }

        TearDown();
    }

    // 測試完整工作流程
    void TestFullWorkflow()
    {
        SetUp();

        // 獲取驅動器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineRegistry* registry = driver.GetRegistry();
        TradingPipelineExplorer* explorer = driver.GetExplorer();
        TradingPipelineContainerManager* manager = driver.GetManager();

        if(registry != NULL && explorer != NULL && manager != NULL)
        {
            // 1. 創建流水線
            MockTradingPipeline* initPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("InitPipeline", INIT_START, driver);
            MockTradingPipeline* tickPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("TickPipeline", TICK_DATA_FEED, driver);

            // 2. 註冊流水線
            bool initRegistered = registry.Register(initPipeline);
            bool tickRegistered = registry.Register(tickPipeline);

            // 3. 探索流水線
            ITradingPipeline* foundInitPipeline = explorer.GetPipeline(INIT_START);
            ITradingPipeline* foundTickPipeline = explorer.GetPipeline(TICK_DATA_FEED);

            // 4. 執行流水線
            if(foundInitPipeline != NULL) foundInitPipeline.Execute();
            if(foundTickPipeline != NULL) foundTickPipeline.Execute();

            // 驗證完整工作流程
            if(m_runner != NULL)
            {
                m_runner.RecordResult(Assert::AssertTrue("完整工作流程_初始化流水線註冊", initRegistered));
                m_runner.RecordResult(Assert::AssertTrue("完整工作流程_Tick流水線註冊", tickRegistered));
                m_runner.RecordResult(Assert::AssertNotNull("完整工作流程_找到初始化流水線", foundInitPipeline));
                m_runner.RecordResult(Assert::AssertNotNull("完整工作流程_找到Tick流水線", foundTickPipeline));
                
                if(foundInitPipeline != NULL)
                {
                    m_runner.RecordResult(Assert::AssertTrue("完整工作流程_初始化流水線執行", foundInitPipeline.IsExecuted()));
                }
                if(foundTickPipeline != NULL)
                {
                    m_runner.RecordResult(Assert::AssertTrue("完整工作流程_Tick流水線執行", foundTickPipeline.IsExecuted()));
                }
            }

            // 清理
            delete initPipeline;
            delete tickPipeline;
        }

        TearDown();
    }

    // 測試驅動器生命週期
    void TestDriverLifecycle()
    {
        SetUp();

        // 獲取驅動器
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

        // 驗證初始狀態
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertNotNull("驅動器生命週期_驅動器存在", driver));
            
            if(driver != NULL)
            {
                m_runner.RecordResult(Assert::AssertNotNull("驅動器生命週期_管理器初始化", driver.GetManager()));
                m_runner.RecordResult(Assert::AssertNotNull("驅動器生命週期_註冊器初始化", driver.GetRegistry()));
                m_runner.RecordResult(Assert::AssertNotNull("驅動器生命週期_探索器初始化", driver.GetExplorer()));
            }
        }

        TearDown();
    }

    // 測試組件交互
    void TestComponentInteraction()
    {
        SetUp();

        // 獲取驅動器和組件
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineRegistry* registry = driver.GetRegistry();
        TradingPipelineExplorer* explorer = driver.GetExplorer();
        TradingPipelineContainerManager* manager = driver.GetManager();

        if(registry != NULL && explorer != NULL && manager != NULL)
        {
            // 創建容器
            TradingPipelineContainer* container = new TradingPipelineContainer(
                "InteractionTestContainer",
                "組件交互測試容器",
                "TradingPipelineContainer",
                TRADING_ORDER
            );

            // 創建流水線並添加到容器
            MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("InteractionPipeline1", ORDER_MANAGEMENT, driver);
            MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("InteractionPipeline2", ORDER_MANAGEMENT, driver);

            container.AddPipeline(pipeline1);
            container.AddPipeline(pipeline2);

            // 設置容器到管理器
            bool containerSet = manager.SetContainer(TRADING_ORDER, container);

            // 註冊流水線
            bool pipeline1Registered = registry.Register(pipeline1);
            bool pipeline2Registered = registry.Register(pipeline2);

            // 通過探索器查找
            ITradingPipeline* foundPipeline1 = explorer.GetPipeline(ORDER_MANAGEMENT);

            // 執行管理器
            manager.Execute(TRADING_ORDER);

            // 驗證組件交互
            if(m_runner != NULL)
            {
                m_runner.RecordResult(Assert::AssertTrue("組件交互_容器設置成功", containerSet));
                m_runner.RecordResult(Assert::AssertTrue("組件交互_流水線1註冊成功", pipeline1Registered));
                m_runner.RecordResult(Assert::AssertTrue("組件交互_流水線2註冊成功", pipeline2Registered));
                m_runner.RecordResult(Assert::AssertNotNull("組件交互_探索器找到流水線", foundPipeline1));
                m_runner.RecordResult(Assert::AssertTrue("組件交互_容器執行完成", container.IsExecuted()));
                m_runner.RecordResult(Assert::AssertTrue("組件交互_流水線1執行完成", pipeline1.IsExecuted()));
                m_runner.RecordResult(Assert::AssertTrue("組件交互_流水線2執行完成", pipeline2.IsExecuted()));
            }

            // 清理
            delete container;
            delete pipeline1;
            delete pipeline2;
        }

        TearDown();
    }
};
