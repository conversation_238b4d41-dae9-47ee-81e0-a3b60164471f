#property strict

#include "../TestFramework.mqh"
#include "MockTradingPipeline.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| 基本流水線整合測試類                                             |
//+------------------------------------------------------------------+
class TestTradingPipelineIntegration : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineIntegration(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineIntegration"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestSimpleWorkflow();
        TestContainerWorkflow();
        TestManagerWorkflow();
        TestErrorHandling();
        TestMixedResults();
        TestNestedContainers();
        TestLargeScale();
    }

private:
    // 測試簡單工作流程
    void TestSimpleWorkflow()
    {
        SetUp();

        // 創建模擬流水線
        MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("DataFeed");
        MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("SignalProcessing");
        MockTradingPipeline* pipeline3 = MockTradingPipelineFactory::CreateSuccessfulPipeline("OrderManagement");

        // 執行流水線
        pipeline1.Execute();
        pipeline2.Execute();
        pipeline3.Execute();

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_流水線1執行", pipeline1.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_流水線2執行", pipeline2.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_流水線3執行", pipeline3.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("簡單工作流程_執行次數1", 1, pipeline1.GetExecutionCount()));
            m_runner.RecordResult(Assert::AssertEquals("簡單工作流程_執行次數2", 1, pipeline2.GetExecutionCount()));
            m_runner.RecordResult(Assert::AssertEquals("簡單工作流程_執行次數3", 1, pipeline3.GetExecutionCount()));
        }

        // 清理
        delete pipeline1;
        delete pipeline2;
        delete pipeline3;

        TearDown();
    }

    // 測試容器工作流程
    void TestContainerWorkflow()
    {
        SetUp();

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            "TestContainer",
            "測試容器",
            "TradingPipelineContainer",
            TRADING_TICK
        );

        // 創建子流水線
        MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("Child1");
        MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("Child2");

        // 添加到容器
        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        // 執行容器
        container.Execute();

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("容器工作流程_容器執行", container.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("容器工作流程_子流水線1執行", pipeline1.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("容器工作流程_子流水線2執行", pipeline2.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("容器工作流程_子流水線數量", 2, container.GetPipelineCount()));
        }

        // 清理
        delete container;

        TearDown();
    }

    // 測試管理器工作流程
    void TestManagerWorkflow()
    {
        SetUp();

        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");

        // 創建容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer(
            "Container1", "容器1", "TradingPipelineContainer", TRADING_INIT
        );
        TradingPipelineContainer* container2 = new TradingPipelineContainer(
            "Container2", "容器2", "TradingPipelineContainer", TRADING_TICK
        );

        // 添加流水線到容器
        MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("Pipeline1");
        MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("Pipeline2");

        container1.AddPipeline(pipeline1);
        container2.AddPipeline(pipeline2);

        // 設置容器到管理器
        manager.SetContainer(TRADING_INIT, container1);
        manager.SetContainer(TRADING_TICK, container2);

        // 執行管理器
        manager.Execute(TRADING_INIT);

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("管理器工作流程_管理器執行", manager.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("管理器工作流程_容器1執行", container1.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("管理器工作流程_容器2執行", container2.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("管理器工作流程_容器數量", 2, manager.GetContainerCount()));
        }

        // 清理
        delete manager;

        TearDown();
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        SetUp();

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            "ErrorTestContainer",
            "錯誤測試容器",
            "TradingPipelineContainer",
            TRADING_INIT
        );

        // 創建成功和失敗的流水線
        MockTradingPipeline* successPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("Success");
        MockTradingPipeline* failedPipeline = MockTradingPipelineFactory::CreateFailedPipeline("Failed", "測試錯誤");

        // 添加到容器
        container.AddPipeline(successPipeline);
        container.AddPipeline(failedPipeline);

        // 執行容器
        container.Execute();

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("錯誤處理_容器執行", container.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("錯誤處理_成功流水線執行", successPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("錯誤處理_失敗流水線執行", failedPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("錯誤處理_成功流水線結果", successPipeline.GetResult().IsSuccess()));
            m_runner.RecordResult(Assert::AssertFalse("錯誤處理_失敗流水線結果", failedPipeline.GetResult().IsSuccess()));
        }

        // 清理
        delete container;

        TearDown();
    }

    // 測試混合結果
    void TestMixedResults()
    {
        SetUp();

        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("MixedTestManager");

        // 創建多個容器，包含成功和失敗的流水線
        TradingPipelineContainer* successContainer = new TradingPipelineContainer(
            "SuccessContainer", "成功容器", "TradingPipelineContainer", TRADING_TICK
        );
        TradingPipelineContainer* mixedContainer = new TradingPipelineContainer(
            "MixedContainer", "混合容器", "TradingPipelineContainer", TRADING_DEINIT
        );

        // 添加流水線
        MockTradingPipeline* success1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("Success1");
        MockTradingPipeline* success2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("Success2");
        MockTradingPipeline* failed1 = MockTradingPipelineFactory::CreateFailedPipeline("Failed1");

        successContainer.AddPipeline(success1);
        successContainer.AddPipeline(success2);
        mixedContainer.AddPipeline(failed1);

        // 設置到管理器
        manager.SetContainer(TRADING_TICK, successContainer);
        manager.SetContainer(TRADING_DEINIT, mixedContainer);

        // 執行
        manager.Execute(TRADING_TICK);

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("混合結果_管理器執行", manager.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("混合結果_成功容器執行", successContainer.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("混合結果_混合容器執行", mixedContainer.IsExecuted()));
        }

        // 清理
        delete manager;

        TearDown();
    }

    // 測試嵌套容器
    void TestNestedContainers()
    {
        SetUp();

        // 創建主容器
        TradingPipelineContainer* mainContainer = new TradingPipelineContainer(
            "MainContainer", "主容器", "TradingPipelineContainer", TRADING_INIT
        );

        // 創建子容器
        TradingPipelineContainer* subContainer = new TradingPipelineContainer(
            "SubContainer", "子容器", "TradingPipelineContainer", TRADING_TICK
        );

        // 創建流水線
        MockTradingPipeline* mainPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("MainPipeline");
        MockTradingPipeline* subPipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("SubPipeline");

        // 構建嵌套結構
        subContainer.AddPipeline(subPipeline);
        mainContainer.AddPipeline(mainPipeline);
        mainContainer.AddPipeline(subContainer);

        // 執行
        mainContainer.Execute();

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("嵌套容器_主容器執行", mainContainer.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("嵌套容器_子容器執行", subContainer.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("嵌套容器_主流水線執行", mainPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertTrue("嵌套容器_子流水線執行", subPipeline.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("嵌套容器_主容器子項數量", 2, mainContainer.GetPipelineCount()));
            m_runner.RecordResult(Assert::AssertEquals("嵌套容器_子容器子項數量", 1, subContainer.GetPipelineCount()));
        }

        // 清理
        delete mainContainer;

        TearDown();
    }

    // 測試大規模場景
    void TestLargeScale()
    {
        SetUp();

        const int CONTAINER_COUNT = 5;
        const int PIPELINES_PER_CONTAINER = 10;

        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("LargeScaleManager");

        Vector<ENUM_TRADING_EVENT> events;
        TestDataFactory::CreateTestEvents(events);

        // 創建多個容器和流水線
        for(int i = 0; i < CONTAINER_COUNT && i < events.size(); i++)
        {
            string containerName = "Container_" + IntegerToString(i + 1);
            TradingPipelineContainer* container = new TradingPipelineContainer(
                containerName, "大規模測試容器", "TradingPipelineContainer", events.get(i)
            );

            // 為每個容器添加多個流水線
            for(int j = 0; j < PIPELINES_PER_CONTAINER; j++)
            {
                string pipelineName = "Pipeline_" + IntegerToString(i + 1) + "_" + IntegerToString(j + 1);
                MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline(pipelineName);
                container.AddPipeline(pipeline);
            }

            manager.SetContainer(events.get(i), container);
        }

        // 執行
        manager.Execute(TRADING_INIT);

        // 驗證結果
        if(m_runner != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("大規模場景_管理器執行", manager.IsExecuted()));
            m_runner.RecordResult(Assert::AssertEquals("大規模場景_容器數量", CONTAINER_COUNT, manager.GetContainerCount()));
        }

        // 清理
        delete manager;

        TearDown();
    }
};
