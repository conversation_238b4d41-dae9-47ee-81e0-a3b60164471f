#property strict

#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 模擬交易流水線類 - 用於整合測試                                   |
//+------------------------------------------------------------------+
class MockTradingPipeline : public TradingPipeline
{
private:
    bool m_shouldSucceed;           // 是否應該成功
    int m_executionCount;           // 執行次數
    string m_errorMessage;          // 錯誤訊息
    int m_executionDelay;           // 執行延遲（毫秒）
    PipelineResult* m_result;       // 執行結果
    bool m_throwException;          // 是否拋出異常

public:
    // 構造函數
    MockTradingPipeline(string name = "MockPipeline",
                       bool shouldSucceed = true,
                       ENUM_TRADING_STAGE stage = INIT_START,
                       ITradingPipelineDriver* driver = NULL)
        : TradingPipeline(name, "MockTradingPipeline", stage, driver),
          m_shouldSucceed(shouldSucceed),
          m_executionCount(0),
          m_errorMessage(""),
          m_executionDelay(0),
          m_result(NULL),
          m_throwException(false)
    {
        m_result = new PipelineResult(true, "初始化", name, ERROR_LEVEL_INFO);
    }

    // 析構函數
    virtual ~MockTradingPipeline()
    {
        if(m_result != NULL)
        {
            delete m_result;
            m_result = NULL;
        }
    }

    // 設置是否應該成功
    void SetShouldSucceed(bool shouldSucceed) { m_shouldSucceed = shouldSucceed; }

    // 設置錯誤訊息
    void SetErrorMessage(string errorMessage) { m_errorMessage = errorMessage; }

    // 設置執行延遲
    void SetExecutionDelay(int delayMs) { m_executionDelay = delayMs; }

    // 設置是否拋出異常
    void SetThrowException(bool throwException) { m_throwException = throwException; }

    // 獲取執行次數
    int GetExecutionCount() const { return m_executionCount; }

    // 獲取執行結果
    PipelineResult* GetResult() const { return m_result; }

    // 重置狀態
    virtual void Restore() override
    {
        TradingPipeline::Restore();
        m_executionCount = 0;
        if(m_result != NULL)
        {
            delete m_result;
        }
        m_result = new PipelineResult(true, "重置", GetName(), ERROR_LEVEL_INFO);
    }

protected:
    // 主要執行邏輯
    virtual void Main() override
    {
        m_executionCount++;

        // 模擬執行延遲
        if(m_executionDelay > 0)
        {
            Sleep(m_executionDelay);
        }

        // 模擬異常
        if(m_throwException)
        {
            Print("MockTradingPipeline 拋出異常: ", GetName());
            return;
        }

        // 設置執行結果
        if(m_result != NULL)
        {
            delete m_result;
        }

        if(m_shouldSucceed)
        {
            m_result = new PipelineResult(true, "MockTradingPipeline 執行成功: " + GetName(), GetName(), ERROR_LEVEL_INFO);
            Print("✅ MockTradingPipeline 成功執行: ", GetName(), " (第", m_executionCount, "次)");
        }
        else
        {
            string errorMsg = m_errorMessage != "" ? m_errorMessage : "MockTradingPipeline 執行失敗";
            m_result = new PipelineResult(false, errorMsg + ": " + GetName(), GetName(), ERROR_LEVEL_ERROR);
            Print("❌ MockTradingPipeline 執行失敗: ", GetName(), " - ", errorMsg);
        }
    }
};

//+------------------------------------------------------------------+
//| 模擬流水線工廠類                                                 |
//+------------------------------------------------------------------+
class MockTradingPipelineFactory
{
public:
    // 創建成功的模擬流水線
    static MockTradingPipeline* CreateSuccessfulPipeline(string name,
                                                         ENUM_TRADING_STAGE stage = INIT_START,
                                                         ITradingPipelineDriver* driver = NULL)
    {
        MockTradingPipeline* pipeline = new MockTradingPipeline(name, true, stage, driver);
        return pipeline;
    }

    // 創建失敗的模擬流水線
    static MockTradingPipeline* CreateFailedPipeline(string name,
                                                     string errorMessage = "模擬失敗",
                                                     ENUM_TRADING_STAGE stage = INIT_START,
                                                     ITradingPipelineDriver* driver = NULL)
    {
        MockTradingPipeline* pipeline = new MockTradingPipeline(name, false, stage, driver);
        pipeline.SetErrorMessage(errorMessage);
        return pipeline;
    }

    // 創建延遲執行的模擬流水線
    static MockTradingPipeline* CreateDelayedPipeline(string name,
                                                      int delayMs,
                                                      ENUM_TRADING_STAGE stage = INIT_START,
                                                      ITradingPipelineDriver* driver = NULL)
    {
        MockTradingPipeline* pipeline = new MockTradingPipeline(name, true, stage, driver);
        pipeline.SetExecutionDelay(delayMs);
        return pipeline;
    }

    // 創建異常流水線
    static MockTradingPipeline* CreateExceptionPipeline(string name,
                                                        ENUM_TRADING_STAGE stage = INIT_START,
                                                        ITradingPipelineDriver* driver = NULL)
    {
        MockTradingPipeline* pipeline = new MockTradingPipeline(name, false, stage, driver);
        pipeline.SetThrowException(true);
        return pipeline;
    }

    // 創建批量測試流水線
    static void CreateTestPipelines(Vector<MockTradingPipeline*>& pipelines,
                                   int count,
                                   string namePrefix = "TestPipeline",
                                   bool shouldSucceed = true,
                                   ENUM_TRADING_STAGE stage = INIT_START,
                                   ITradingPipelineDriver* driver = NULL)
    {
        for(int i = 0; i < count; i++)
        {
            string name = namePrefix + "_" + IntegerToString(i + 1);
            MockTradingPipeline* pipeline = new MockTradingPipeline(name, shouldSucceed, stage, driver);
            pipelines.add(pipeline);
        }
    }

    // 清理批量流水線
    static void CleanupPipelines(Vector<MockTradingPipeline*>& pipelines)
    {
        for(int i = 0; i < pipelines.size(); i++)
        {
            MockTradingPipeline* pipeline = pipelines.get(i);
            if(pipeline != NULL)
            {
                delete pipeline;
            }
        }
        pipelines.clear();
    }
};

//+------------------------------------------------------------------+
//| 測試數據工廠類                                                   |
//+------------------------------------------------------------------+
class TestDataFactory
{
public:
    // 創建測試用的交易階段數組
    static void CreateTestStages(Vector<ENUM_TRADING_STAGE>& stages)
    {
        stages.add(INIT_START);
        stages.add(TICK_DATA_FEED);
        stages.add(TICK_SIGNAL_ANALYSIS);
        stages.add(TICK_ORDER_MANAGEMENT);
        stages.add(TICK_RISK_CONTROL);
        stages.add(TICK_LOGGING);
        stages.add(DEINIT_COMPLETE);
    }

    // 創建測試用的交易事件數組
    static void CreateTestEvents(Vector<ENUM_TRADING_EVENT>& events)
    {
        events.add(TRADING_INIT);
        events.add(TRADING_TICK);
        events.add(TRADING_DEINIT);
    }

    // 創建測試用的容器名稱數組
    static void CreateTestContainerNames(Vector<string>& names)
    {
        names.add("InitContainer");
        names.add("TickContainer");
        names.add("DeinitContainer");
    }
};
